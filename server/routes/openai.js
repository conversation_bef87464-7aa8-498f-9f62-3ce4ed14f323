// server/routes/openai.js
const express = require('express');
const { Configuration, OpenAIApi } = require('openai');
const quota = require('../utils/quotaLimiter');
const { ragGenerate } = require('../utils/ragService');

// Configuration OpenAI
const configuration = new Configuration({ apiKey: process.env.OPENAI_API_KEY });
const openai = new OpenAIApi(configuration);

const router = express.Router();

router.post('/', quota, async (req, res) => {
  try {
    const { conversation } = req.body;

    // 1) Contexte via RAG
    const contextChunks = await ragGenerate(conversation);

    // 2) Résumé de la conversation
    const summaryResp = await openai.createChatCompletion({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un assistant B2B expert en synthèse.' },
        ...contextChunks.map(chunk => ({ role: 'system', content: chunk })),
        { role: 'user', content: 'Résume cette conversation en une phrase concise.' }
      ],
      max_tokens: 60,
      temperature: 0.3,
    });
    const summary = summaryResp.data.choices[0].message.content.trim();

    // 3) Génération de 3 messages de closing concrets adaptés au contexte
    const closingResp = await openai.createChatCompletion({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: 'Tu es un expert en prospection et closing B2B. Ton objectif : closer un contrat rapidement. Réponds toujours avec professionnalisme et sens de l\'urgence.' },
        { role: 'system', content: `Résumé : ${summary}` },
        { role: 'system', content: `Contexte complet : ${conversation}` },
        { role: 'user', content: `En te basant sur le résumé : "${summary}" et sur le contexte complet de la conversation, propose 3 réponses courtes (1–2 phrases), pertinentes et orientées action, afin de faire avancer l’échange ou de conclure un accord.` }
      ],
      max_tokens: 200,
      temperature: 0.8,
    });
    const suggestions = closingResp.data.choices
      .map(choice => choice.message.content.trim())
      .slice(0, 3);

    // 4) Envoi de la réponse
    res.json({ summary, suggestions, quota: res.locals.quota });

  } catch (error) {
    console.error('OpenAI error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

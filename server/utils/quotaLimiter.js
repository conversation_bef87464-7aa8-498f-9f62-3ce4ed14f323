const { createClient } = require('redis');
const client = createClient({ url: process.env.REDIS_URL });
client.connect();
const LIMIT = parseInt(process.env.QUOTA_LIMIT || '100', 10);
module.exports = async (req, res, next) => {
  const key = `quota:${req.ip}:${new Date().toISOString().slice(0,10)}`;
  const used = await client.incr(key);
  if (used === 1) await client.expire(key, 86400);
  if (used > LIMIT) return res.status(429).json({ error: 'Quota dépassé' });
  res.locals.quota = { used, limit: LIMIT };
  next();
};
